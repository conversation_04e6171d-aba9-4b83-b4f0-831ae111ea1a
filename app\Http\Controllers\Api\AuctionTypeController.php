<?php

namespace App\Http\Controllers\Api;

use App\Models\AuctionType;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionTypeResource;
use App\Http\Resources\AuctionTypeCollection;
use App\Http\Requests\AuctionTypeStoreRequest;
use App\Http\Requests\AuctionTypeUpdateRequest;
use Facades\App\Cache\Repo;

class AuctionTypeController extends Controller
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize('view-any', AuctionType::class);

        $search = $request->get('search', '');
        $type = $request->get('type', '');
        $perPage = $request->get('per_page', 20);
        $page = $request->get('page', 1);

        $query = AuctionType::with(['items' => function($q) {
                $q->whereNull('closed_by');
            }, 'createdBy'])
            ->whereIn('type', ['online', 'cash'])
            ->search($search)
            ->latest();

        // Apply type filter if provided
        if ($type) {
            $query->where('type', $type);
        }

        $auctionTypes = $query->paginate($perPage, ['*'], 'page', $page);

        return new AuctionTypeCollection($auctionTypes);
    }

    /**
     * Get live auction listings with pagination
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function liveAuctionListings(Request $request)
    {
        $this->authorize('view-any', AuctionType::class);

        $search = $request->get('search', '');
        $perPage = $request->get('per_page', 20);
        $page = $request->get('page', 1);

        $query = AuctionType::with(['items' => function($q) {
                $q->whereNull('closed_by');
            }])
            ->where('type', 'live')
            ->search($search)
            ->latest();

        $auctionTypes = $query->paginate($perPage, ['*'], 'page', $page);

        return new AuctionTypeCollection($auctionTypes);
    }

    /**
     * @param \App\Http\Requests\AuctionTypeStoreRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(AuctionTypeStoreRequest $request)
    {
        $this->authorize('create', AuctionType::class);

        $validated = $request->validated();

        $auctionType = AuctionType::create($validated);

        // Handle items assignment for auction listings
        if ($request->has('items') && is_array($request->items)) {
            \App\Models\Item::whereIn("id", $request->items)->update([
                "auction_type_id" => $auctionType->id,
            ]);
        }

        return new AuctionTypeResource($auctionType->load('items'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request, AuctionType $auctionType)
    {
        $this->authorize('view', $auctionType);

        return new AuctionTypeResource($auctionType->load('items'));
    }

    /**
     * @param \App\Http\Requests\AuctionTypeUpdateRequest $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function update(
        AuctionTypeUpdateRequest $request,
        AuctionType $auctionType
    ) {
        $this->authorize('update', $auctionType);

        $validated = $request->validated();

        $auctionType->update($validated);

        // Handle items assignment for auction listings
        if ($request->has('items') && is_array($request->items)) {
            // First, remove this auction type from all items
            \App\Models\Item::where('auction_type_id', $auctionType->id)->update([
                'auction_type_id' => null,
            ]);

            // Then assign the new items
            \App\Models\Item::whereIn("id", $request->items)->update([
                "auction_type_id" => $auctionType->id,
            ]);
        }

        return new AuctionTypeResource($auctionType->load('items'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\AuctionType $auctionType
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, AuctionType $auctionType)
    {
        $this->authorize('delete', $auctionType);

        $auctionType->delete();

        return response()->noContent();
    }
}
