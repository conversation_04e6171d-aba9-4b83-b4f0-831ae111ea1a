

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Refund List</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <a href="<?php echo e(route('transactions.index')); ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Transactions
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="card-title mb-0">Refund Transactions</h5>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                        <i class="bi bi-funnel me-1"></i> Filters
                    </button>
                </div>
            </div>
        </div>

        <div class="collapse" id="filterCollapse">
            <div class="card-body border-bottom">
                <form method="GET" class="mb-0">
                    <input type="hidden" name="from" value="<?php echo e(request()->from); ?>">
                    <input type="hidden" name="to" value="<?php echo e(request()->to); ?>">

                    <div class="row g-3">
                        <div class="col-md-5">
                            <div class="d-flex align-items-center h-100">
                                <div class="flex-shrink-0">
                                    <label class="form-label mb-0 me-2">Date Range:</label>
                                </div>
                                <div class="flex-grow-1">
                                    <button id="js-daterangepicker-predefined" type="button" class="btn btn-sm btn-outline-secondary w-100">
                                        <i class="bi-calendar-week me-1"></i>
                                        <span class="js-daterangepicker-predefined-preview"></span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-5">
                            <div class="d-flex align-items-center h-100">
                                <div class="flex-shrink-0">
                                    <label class="form-label mb-0 me-2">Type:</label>
                                </div>
                                <div class="flex-grow-1">
                                    <select class="form-select form-select-sm" name="auction_type_id" autocomplete="off">
                                        <option value="0">All Types</option>
                                        <?php $__currentLoopData = App\Models\AuctionType::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auction_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option <?php if(request()->auction_type_id == $auction_type->id): ?> selected <?php endif; ?> value="<?php echo e($auction_type->id); ?>">
                                            <?php echo e($auction_type->name ?? ''); ?>

                                        </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="bi bi-search me-1"></i> Apply
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th>#</th>
                            <th class="text-left">
                                Customer Name
                            </th>
                            <th class="text-right">
                                <?php echo app('translator')->get('crud.transactions.inputs.amount_total'); ?>
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.transactions.inputs.reference_number'); ?>
                            </th>
                            <th class="text-left">
                                Date
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="<?php if($transaction->user_id): ?> text-success <?php endif; ?>">
                            <td><?php echo e($key + 1); ?></td>
                            <td>
                                <?php echo e(optional($transaction->user)->name ?? '-'); ?>

                            </td>
                            <td class="text-right"><?php echo e(_money($transaction->amount)); ?></td>
                            <td><?php echo e($transaction->reference_number ?? '-'); ?></td>
                            <td><?php echo e($transaction->created_at ?? '-'); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">No refund transactions found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="bg-light">
                            <th></th>
                            <th></th>
                            <th class="text-right">
                                <span class="h4 text-info">
                                    <?php echo e(_money($transactions->sum('amount'))); ?>

                                </span>
                            </th>
                            <th></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn(".js-datatable", null, [0, 'asc'])
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/reports/refund-list-report.blade.php ENDPATH**/ ?>