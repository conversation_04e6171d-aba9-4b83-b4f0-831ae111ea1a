<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Auction;
use App\Models\Item;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Facades\App\Cache\Repo;

class ReportsController extends Controller
{
    /**
     * Get winners report data
     */
    public function winnersReport(Request $request)
    {
        // Ensure user has admin access
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // Get winners data using existing Repo method
            $auctions = Repo::winnersReport();
            
            // Transform data for Vue component
            $winners = $auctions->map(function ($auction) {
                return [
                    'id' => $auction->id,
                    'auction_id' => $auction->id,
                    'auction_title' => $auction->item->name ?? 'N/A',
                    'winner_name' => $auction->user->name ?? 'N/A',
                    'winner_email' => $auction->user->email ?? 'N/A',
                    'winning_amount' => $auction->bid_amount ?? 0,
                    'end_date' => $auction->date_to,
                    'status' => $auction->closed_by ? 'completed' : 'pending',
                    'branch_id' => $auction->branch_id,
                    'created_at' => $auction->created_at,
                ];
            });

            // Get summary statistics
            $summary = [
                'totalWinners' => $winners->count(),
                'totalAmount' => $winners->sum('winning_amount'),
                'avgWinningBid' => $winners->count() > 0 ? $winners->avg('winning_amount') : 0,
                'completedAuctions' => $winners->where('status', 'completed')->count(),
            ];

            return response()->json([
                'winners' => $winners,
                'summary' => $summary,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch winners report: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get sales report data
     */
    public function salesReport(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // Get sales data using existing Repo method
            $items = Repo::salesReport();
            
            // Transform data for Vue component
            $sales = $items->map(function ($item) {
                $auction = $item->auctions()->whereNotNull('tagged_by')->first();
                return [
                    'id' => $item->id,
                    'item_id' => $item->id,
                    'item_title' => $item->name,
                    'item_image' => $item->image,
                    'auction_id' => $auction->id ?? null,
                    'auction_title' => $auction->name ?? 'N/A',
                    'winner_name' => $auction->user->name ?? 'N/A',
                    'winner_email' => $auction->user->email ?? 'N/A',
                    'sale_price' => $auction->bid_amount ?? $item->bid_amount ?? 0,
                    'commission' => ($auction->bid_amount ?? $item->bid_amount ?? 0) * 0.1, // 10% commission
                    'sale_date' => $auction->date_to ?? $item->date_to,
                    'status' => $auction && $auction->closed_by ? 'completed' : 'pending',
                    'category' => $item->auctionType->name ?? 'N/A',
                    'branch_id' => $item->branch_id,
                ];
            });

            // Get summary statistics
            $totalSales = $sales->sum('sale_price');
            $summary = [
                'totalSales' => $totalSales,
                'itemsSold' => $sales->count(),
                'avgSalePrice' => $sales->count() > 0 ? $sales->avg('sale_price') : 0,
                'totalAuctions' => $sales->pluck('auction_id')->unique()->count(),
            ];

            return response()->json([
                'sales' => $sales,
                'summary' => $summary,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch sales report: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get inventory report data
     */
    public function inventoryReport(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // Get inventory data using existing Repo method
            $items = Repo::inventoryReport();
            
            // Transform data for Vue component
            $inventory = $items->map(function ($item) {
                $hasActiveAuction = $item->auctions()->whereNull('closed_by')->exists();
                $isSold = $item->auctions()->whereNotNull('tagged_by')->exists();
                
                $status = 'available';
                if ($isSold) {
                    $status = 'sold';
                } elseif ($hasActiveAuction) {
                    $status = 'in_auction';
                }

                return [
                    'id' => $item->id,
                    'title' => $item->name,
                    'image' => $item->image,
                    'category' => $item->auctionType->name ?? 'N/A',
                    'status' => $status,
                    'starting_price' => $item->bid_amount ?? 0,
                    'current_value' => $item->target_amount ?? $item->bid_amount ?? 0,
                    'branch' => $item->branch->name ?? 'N/A',
                    'branch_id' => $item->branch_id,
                    'created_at' => $item->created_at,
                ];
            });

            // Get summary statistics
            $summary = [
                'totalItems' => $inventory->count(),
                'availableItems' => $inventory->where('status', 'available')->count(),
                'inAuction' => $inventory->where('status', 'in_auction')->count(),
                'soldItems' => $inventory->where('status', 'sold')->count(),
            ];

            return response()->json([
                'items' => $inventory,
                'summary' => $summary,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch inventory report: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get refunds report data
     */
    public function refundsReport(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // Get refunds data using existing Repo method
            $transactions = Repo::refundListReport();
            
            // Transform data for Vue component
            $refunds = $transactions->map(function ($transaction) {
                $auction = $transaction->auction;
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->id,
                    'customer_name' => $transaction->user->name ?? 'N/A',
                    'customer_email' => $transaction->user->email ?? 'N/A',
                    'auction_id' => $auction->id ?? null,
                    'auction_title' => $auction->item->name ?? 'N/A',
                    'amount' => $transaction->amount ?? 0,
                    'reason' => 'Auction refund', // Default reason
                    'status' => $transaction->closed_by ? 'completed' : 'pending',
                    'created_at' => $transaction->created_at,
                    'branch_id' => $transaction->branch_id,
                ];
            });

            // Get summary statistics
            $summary = [
                'totalRefunds' => $refunds->count(),
                'pendingRefunds' => $refunds->where('status', 'pending')->count(),
                'completedRefunds' => $refunds->where('status', 'completed')->count(),
                'totalAmount' => $refunds->sum('amount'),
            ];

            return response()->json([
                'refunds' => $refunds,
                'summary' => $summary,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch refunds report: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get deposits report data
     */
    public function depositsReport(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            // Get deposits data using existing Repo method
            $transactions = Repo::depositsReport();
            
            // Transform data for Vue component
            $deposits = $transactions->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'transaction_id' => $transaction->id,
                    'reference_number' => $transaction->reference_number ?? 'N/A',
                    'customer_name' => $transaction->user->name ?? 'N/A',
                    'customer_email' => $transaction->user->email ?? 'N/A',
                    'amount' => $transaction->amount ?? 0,
                    'payment_method' => 'bank_transfer', // Default method
                    'status' => $transaction->closed_by ? 'confirmed' : 'pending',
                    'created_at' => $transaction->created_at,
                    'branch_id' => $transaction->branch_id,
                ];
            });

            // Get summary statistics
            $summary = [
                'totalDeposits' => $deposits->count(),
                'confirmedDeposits' => $deposits->where('status', 'confirmed')->count(),
                'pendingDeposits' => $deposits->where('status', 'pending')->count(),
                'totalAmount' => $deposits->sum('amount'),
            ];

            return response()->json([
                'deposits' => $deposits,
                'summary' => $summary,
                'success' => true
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch deposits report: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }

    /**
     * Get branches for filter dropdowns
     */
    public function getBranches(Request $request)
    {
        if (!auth()->user() || auth()->user()->isCustomer() || auth()->user()->isSupplier()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $branches = Branch::select('id', 'name')->get();
            return response()->json(['branches' => $branches, 'success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to fetch branches: ' . $e->getMessage(),
                'success' => false
            ], 500);
        }
    }
}
