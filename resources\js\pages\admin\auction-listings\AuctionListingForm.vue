<template>
  <AdminFormTemplate
    :title="isEditing ? 'Edit Auction Listing' : 'Create Auction Listing'"
    :subtitle="isEditing ? 'Update auction listing details' : 'Create a new auction listing'"
    :loading="loading"
    :error="error"
    :breadcrumbs="breadcrumbs"
    @submit="handleSubmit"
    @cancel="handleCancel"
  >
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Basic Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Auction Name -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.name"
              label="Auction Name"
              placeholder="Enter auction name"
              :error="errors.name"
            />
          </div>

          <!-- Code -->
          <FormField
            v-model="form.code"
            label="Auction Code"
            placeholder="Enter auction code"
            :error="errors.code"
          />



          <!-- Items Selection -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Select Items *
            </label>
            <Select
              v-model="selectedItemObjects"
              :items="itemSelectOptions"
              placeholder="Select items for this auction..."
              :multi-select="true"
              searchable
              search-placeholder="Search items..."
              :class="errors.item_ids ? 'border-red-300' : ''"
            />
            <p v-if="errors.item_ids" class="mt-1 text-sm text-red-600">{{ errors.item_ids }}</p>
          </div>

          <!-- Branch -->
          <FormField
            v-model="form.branch_id"
            label="Branch"
            type="select"
            placeholder="Select branch"
            :options="branchOptions"
            :error="errors.branch_id"
          />

          <!-- Description -->
          <div class="md:col-span-2">
            <FormField
              v-model="form.description"
              label="Description"
              type="textarea"
              placeholder="Enter auction description"
              :error="errors.description"
              rows="4"
            />
          </div>
        </div>
      </Card>

      <!-- Bidding Information -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Bidding Information</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Starting Bid Amount -->
          <FormField
            v-model="form.bid_amount"
            label="Starting Bid Amount"
            type="number"
            step="0.01"
            min="0"
            placeholder="0.00"
            :error="errors.bid_amount"
          />

          <!-- Owner/Bidder -->
          <FormField
            v-model="form.user_id"
            label="Owner/Bidder"
            type="select"
            placeholder="Select owner"
            :options="userOptions"
            :error="errors.user_id"
          />
        </div>
      </Card>

      <!-- Schedule -->
      <Card class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Start Date -->
          <FormField
            v-model="form.date_from"
            label="Start Date & Time"
            type="datetime-local"
            :error="errors.date_from"
          />

          <!-- End Date -->
          <FormField
            v-model="form.date_to"
            label="End Date & Time"
            type="datetime-local"
            :error="errors.date_to"
          />
        </div>
      </Card>

      <!-- Selected Items Preview -->
      <Card v-if="selectedItems.length > 0" class="p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">
          Selected Items ({{ selectedItems.length }})
        </h3>

        <div class="space-y-4">
          <div
            v-for="item in selectedItems"
            :key="item.id"
            class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg"
          >
            <img
              :src="item.image || '/img/product.jpeg'"
              :alt="item.name"
              class="w-16 h-16 rounded-lg object-cover flex-shrink-0"
            />
            <div class="flex-1 min-w-0">
              <h4 class="text-base font-medium text-gray-900 truncate">{{ item.name }}</h4>
              <p class="text-sm text-gray-600 mt-1">Ref: {{ item.reference_number || 'N/A' }}</p>
              <p class="text-sm text-gray-600">Target: {{ formatCurrency(item.target_amount) }}</p>
              <p v-if="item.description" class="text-sm text-gray-500 mt-1 line-clamp-2">
                {{ item.description }}
              </p>
              <div class="flex items-center space-x-2 mt-2">
                <AdminBadge
                  :variant="item.closed_by ? 'success' : 'warning'"
                  size="sm"
                >
                  {{ item.closed_by ? 'Sold' : 'Available' }}
                </AdminBadge>
                <AdminBadge
                  v-if="item.auctionType"
                  :variant="getAuctionTypeBadgeVariant(item.auctionType.type)"
                  size="sm"
                >
                  {{ item.auctionType.name }}
                </AdminBadge>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-6 border-t">
        <Button
          type="button"
          variant="outline"
          @click="handleCancel"
          :disabled="submitting"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="primary"
          :loading="submitting"
          :disabled="!isFormValid"
        >
          {{ isEditing ? 'Update Auction' : 'Create Auction' }}
        </Button>
      </div>
    </form>
  </AdminFormTemplate>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { AdminFormTemplate } from '@/components/admin/templates';
import { AdminBadge } from '@/components/admin/ui';
import { Card, Button, FormField, Select } from '@/components/ui';
import { useAdminItems } from '@/stores/admin/items';
import { useAdminBranches } from '@/stores/admin/branches';
import { useNotifications } from '@/composables/useNotifications';
import axios from 'axios';

// Router
const router = useRouter();
const route = useRoute();

// Stores
const itemsStore = useAdminItems();
const branchesStore = useAdminBranches();

// Notifications
const { showNotification } = useNotifications();

// State
const loading = ref(false);
const submitting = ref(false);
const error = ref<string | null>(null);
const users = ref<any[]>([]);

// Form data
const form = reactive({
  name: '',
  code: '',
  item_ids: [], // Array of selected item IDs
  branch_id: '',
  description: '',
  bid_amount: '',
  user_id: '',
  date_from: '',
  date_to: ''
});

// Form errors
const errors = reactive({
  name: '',
  code: '',
  item_ids: '',
  branch_id: '',
  description: '',
  bid_amount: '',
  user_id: '',
  date_from: '',
  date_to: ''
});

// Computed
const isEditing = computed(() => !!route.params.id);
const auctionId = computed(() => route.params.id ? parseInt(route.params.id as string) : null);

const breadcrumbs = computed(() => [
  { label: 'Dashboard', href: '/admin-spa' },
  { label: 'Auction Listings', href: '/admin-spa/auction-listings/list' },
  { label: isEditing.value ? 'Edit Auction' : 'Create Auction' }
]);

const isFormValid = computed(() => {
  return form.name !== '' && form.item_ids.length > 0;
});

const selectedItems = computed(() => {
  if (!form.item_ids.length) return [];
  return itemsStore.itemsList.filter(item =>
    form.item_ids.includes(item.id.toString())
  );
});

// For backward compatibility with the preview section
const selectedItem = computed(() => selectedItems.value[0] || null);

// Options for dropdowns
const itemSelectOptions = computed(() =>
  itemsStore.itemsList
    .filter(item => !item.closed_by) // Only show available items
    .map(item => ({
      key: item.id.toString(),
      label: `${item.name} (${item.reference_number || 'No ref'})`,
      value: item.id.toString(),
      description: item.description || undefined
    }))
);

// Handle selected items for the Select component
const selectedItemObjects = computed({
  get: () => {
    return itemSelectOptions.value.filter(option =>
      form.item_ids.includes(option.value)
    );
  },
  set: (selectedItems) => {
    form.item_ids = selectedItems.map(item => item.value);
  }
});

const branchOptions = computed(() => [
  ...branchesStore.branches.map(branch => ({
    label: branch.name,
    value: branch.id.toString()
  }))
]);

const userOptions = computed(() => [
  ...users.value.map(user => ({
    label: `${user.name} (${user.email})`,
    value: user.id.toString()
  }))
]);

// Methods
const loadAuctionListing = async () => {
  if (!auctionId.value) return;

  loading.value = true;
  try {
    // Use auction-types API endpoint since auction listings are AuctionType records
    const response = await axios.get(`/api/admin/auction-types/${auctionId.value}`);
    const auctionListing = response.data.data || response.data;

    // Populate form with auction listing data
    form.name = auctionListing.name || '';
    form.code = auctionListing.code || '';
    form.item_ids = auctionListing.items?.map(item => item.id.toString()) || [];
    form.branch_id = auctionListing.branch_id?.toString() || '';
    form.description = auctionListing.description || '';
    form.bid_amount = auctionListing.bid_amount?.toString() || '';
    form.user_id = auctionListing.created_by?.toString() || '';
    form.date_from = auctionListing.date_from ? formatDateTimeLocal(auctionListing.date_from) : '';
    form.date_to = auctionListing.date_to ? formatDateTimeLocal(auctionListing.date_to) : '';
  } catch (err) {
    error.value = 'Failed to load auction listing';
    showNotification('Failed to load auction listing', 'error');
  } finally {
    loading.value = false;
  }
};

const loadUsers = async () => {
  try {
    const response = await axios.get('/api/admin/users');
    users.value = response.data.data || response.data;
  } catch (err) {
    console.error('Failed to load users:', err);
  }
};

const handleSubmit = async () => {
  if (!isFormValid.value) return;

  submitting.value = true;
  clearErrors();

  try {
    // Create AuctionType data for auction listing
    const auctionListingData = {
      name: form.name || undefined,
      description: form.description || undefined,
      bid_amount: form.bid_amount ? parseFloat(form.bid_amount) : undefined,
      date_from: form.date_from || undefined,
      date_to: form.date_to || undefined,
      type: 'live', // Always set type to 'live' for auction listings
      created_by: form.user_id ? parseInt(form.user_id) : undefined,
      items: form.item_ids.map(id => parseInt(id)) // Send selected item IDs
    };

    if (isEditing.value && auctionId.value) {
      const response = await axios.put(`/api/admin/auction-types/${auctionId.value}`, auctionListingData);
      showNotification('Auction listing updated successfully', 'success');
    } else {
      const response = await axios.post('/api/admin/auction-types', auctionListingData);
      showNotification('Auction listing created successfully', 'success');
    }

    router.push('/admin-spa/auction-listings/list');
  } catch (err: any) {
    if (err.response?.data?.errors) {
      Object.assign(errors, err.response.data.errors);
    } else {
      error.value = err.response?.data?.message || 'Failed to save auction listing';
      showNotification('Failed to save auction listing', 'error');
    }
  } finally {
    submitting.value = false;
  }
};

const handleCancel = () => {
  router.push('/admin-spa/auction-listings/list');
};

const clearErrors = () => {
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = '';
  });
  error.value = null;
};

// Utility methods
const getAuctionTypeBadgeVariant = (type: string) => {
  switch (type) {
    case 'live': return 'success';
    case 'online': return 'primary';
    case 'cash': return 'warning';
    default: return 'secondary';
  }
};

const formatCurrency = (amount: number | null | undefined) => {
  if (!amount) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

const formatDateTimeLocal = (dateString: string) => {
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16);
};

// Watch for pre-selected item from query params
watch(() => route.query.item_id, (itemId) => {
  if (itemId && !isEditing.value) {
    form.item_ids = [itemId.toString()];
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  await Promise.all([
    itemsStore.fetchItems({ per_page: 100 }), // Load more items for selection
    branchesStore.fetchBranches(),
    loadUsers()
  ]);

  if (isEditing.value) {
    await loadAuctionListing();
  }
});

const formatDateTimeLocal = (dateString: string) => {
  const date = new Date(dateString);
  return date.toISOString().slice(0, 16);
};

// Watch for pre-selected item from query params
watch(() => route.query.item_id, (itemId) => {
  if (itemId && !isEditing.value) {
    form.item_id = itemId.toString();
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  await Promise.all([
    auctionTypesStore.fetchAuctionTypes(),
    itemsStore.fetchItems({ per_page: 100 }), // Load more items for selection
    branchesStore.fetchBranches(),
    loadUsers()
  ]);

  if (isEditing.value) {
    await loadAuction();
  }
});
</script>
