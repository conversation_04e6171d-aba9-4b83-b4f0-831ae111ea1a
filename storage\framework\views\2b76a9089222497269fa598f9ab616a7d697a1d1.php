

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Deposits</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Role::class)): ?>
                <a href="#" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target=".import-statement-modal">
                    <i class="bi bi-upload me-1"></i>
                    Upload Deposits
                </a>
                <a href="<?php echo e(route('transactions.create')); ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add Deposit
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filter Deposits</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row align-items-end">
                        <input type="hidden" name="from" value="<?php echo e(request()->from); ?>">
                        <input type="hidden" name="to" value="<?php echo e(request()->to); ?>">

                        <div class="col-md-4 mb-2">
                            <label class="form-label">Date Range</label>
                            <button id="js-daterangepicker-predefined" type="button" class="btn btn-white w-100 border border-dark">
                                <i class="bi-calendar-week me-1"></i>
                                <span class="js-daterangepicker-predefined-preview"></span>
                            </button>
                        </div>

                        <div class="col-md-4 mb-2">
                            <label class="form-label">Select User</label>
                            <select class="js-selec form-select" name="user_id" autocomplete="off"
                                data-hs-tom-select-options='{
                                        "dropdownWidth": "300px",
                                        "placeholder": "Select user",
                                        "dropdownLeft": true
                                    }'>
                                <option value="0">All Users</option>
                                <?php $__currentLoopData = Facades\App\Cache\Repo::getStaffs(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option <?php if(request()->user_id == $user->id): ?> selected <?php endif; ?> value="<?php echo e($user->id); ?>">
                                    <?php echo e($user->name ?? ''); ?>

                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>

                        <div class="col-md-4 mb-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-filter me-1"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Nav -->
    <div class="card mb-4">
        <div class="card-header p-0">
            <ul class="nav nav-tabs card-nav-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="deposits-tab" href="#deposits" data-bs-toggle="pill" data-bs-target="#deposits" role="tab" aria-controls="deposits" aria-selected="true">
                        <i class="bi bi-check-circle me-1"></i> Approved Deposits
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="pending-deposits-tab" href="#pending-deposits" data-bs-toggle="pill" data-bs-target="#pending-deposits" role="tab" aria-controls="pending-deposits" aria-selected="false">
                        <i class="bi bi-hourglass-split me-1"></i> Pending Deposits
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body p-0">
            <!-- Tab Content -->
            <div class="tab-content">
                <div class="tab-pane fade show active p-3" id="deposits" role="tabpanel" aria-labelledby="deposits-tab">
                    <div class="table-responsive">
                        <table class="table approved-deposits table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                            <thead class="thead-light">
                                <tr>
                                    <th>#</th>
                                    <th class="text-left">Customer Fullname</th>
                                    <th class="text-left">Phone</th>
                                    <th class="text-right"><?php echo app('translator')->get('crud.transactions.inputs.amount_total'); ?></th>
                                    <th class="text-left"><?php echo app('translator')->get('crud.transactions.inputs.reference_number'); ?></th>
                                    <th class="text-left">Date</th>
                                    <th class="text-center"><?php echo app('translator')->get('crud.common.actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($key + 1); ?></td>
                                    <td><?php echo e(optional($transaction->user)->name ?? '-'); ?></td>
                                    <td><?php echo e(optional($transaction->user)->phone ?? '-'); ?></td>
                                    <td class="text-right"><?php echo e(_money($transaction->amount)); ?></td>
                                    <td><?php echo e($transaction->reference_number ?? '-'); ?></td>
                                    <td><?php echo e($transaction->created_at ?? '-'); ?></td>
                                    <td class="text-center" style="width: 134px;">
                                        <div role="group" aria-label="Row Actions" class="btn-group">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $transaction)): ?>
                                            <a href="<?php echo e(route('transactions.show', $transaction)); ?>">
                                                <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                                    <i class="bi bi-eye me-1"></i>
                                                    View
                                                </button>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No approved deposits found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="tab-pane fade p-3" id="pending-deposits" role="tabpanel" aria-labelledby="pending-deposits-tab">
                    <div class="table-responsive">
                        <table class="table pending-deposits table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                            <thead class="thead-light">
                                <tr>
                                    <th>#</th>
                                    <th class="text-left">Customer Fullname</th>
                                    <th class="text-left">Phone</th>
                                    <th class="text-right"><?php echo app('translator')->get('crud.transactions.inputs.amount_total'); ?></th>
                                    <th class="text-left"><?php echo app('translator')->get('crud.transactions.inputs.reference_number'); ?></th>
                                    <th class="text-left">Date</th>
                                    <th class="text-center"><?php echo app('translator')->get('crud.common.actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $pendingTransactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($key + 1); ?></td>
                                    <td><?php echo e(optional($transaction->user)->name ?? '-'); ?></td>
                                    <td><?php echo e(optional($transaction->user)->phone ?? '-'); ?></td>
                                    <td class="text-right"><?php echo e(_money($transaction->amount)); ?></td>
                                    <td><?php echo e($transaction->reference_number ?? '-'); ?></td>
                                    <td><?php echo e($transaction->created_at ?? '-'); ?></td>
                                    <td class="text-center" style="width: 134px;">
                                        <div role="group" aria-label="Row Actions" class="btn-group">
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $transaction)): ?>
                                            <a href="<?php echo e(route('transactions.edit', $transaction)); ?>">
                                                <button type="button" class="btn btn-primary btn-sm m-1">
                                                    <i class="bi bi-pencil-square me-1"></i>
                                                    Edit
                                                </button>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $transaction)): ?>
                                            <a href="<?php echo e(route('transactions.show', $transaction)); ?>">
                                                <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                                    <i class="bi bi-eye me-1"></i>
                                                    View
                                                </button>
                                            </a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $transaction)): ?>
                                            <form action="<?php echo e(route('transactions.destroy', $transaction)); ?>" method="POST"
                                                  onsubmit="return confirm('<?php echo e(__('crud.common.are_you_sure')); ?>')">
                                                <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-danger btn-sm m-1">
                                                    <i class="bi bi-trash me-1"></i>
                                                    Delete
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No pending deposits found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- End Tab Content -->
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn('.approved-deposits', null, [5, 'desc']);
    dataTableBtn('.pending-deposits', null, [5, 'desc']);
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/transactions/index.blade.php ENDPATH**/ ?>