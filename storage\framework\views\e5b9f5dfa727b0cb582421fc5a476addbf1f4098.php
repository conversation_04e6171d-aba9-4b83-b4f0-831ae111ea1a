

<?php $__env->startSection('content'); ?>
    <!-- Content -->
    <div class="content container-fluid">
      <!-- Page Header -->
      <div class="page-header d-print-none">
        <div class="row align-items-end">
          <div class="col-sm mb-2 mb-sm-0">
            <h1 class="page-header-title">Sale</h1>
          </div>
          <!-- End Col -->

          <div class="col-sm-auto">
            <a class="btn btn-primary" href="/orders">
              <i class="bi-plus-lg me-1"></i> Back
            </a>            
            <a class="btn btn-primary" href="/orders/create">
              <i class="bi-plus-lg me-1"></i> Add Sale
            </a>
          </div>
          <!-- End Col -->
        </div>
        <!-- End Row -->
      </div>
      <!-- End Page Header -->

      <div class="row">
        <div class="col-lg-8 mb-5 mb-lg-0">
          <!-- Card -->
          <div class="card card-lg mb-5">
            <div class="card-body order"  id="order">
              <div class="row justify-content-lg-between">
                <div class="col-sm order-2 order-sm-1 mb-3">
                  <div class="mb-2">
                    <img class="avatar" width="200" src="<?php echo e(auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg')); ?>" alt="Logo">
                  </div>

                  <h1 class="h2 text-primary"><?php echo e(auth()->user()->getGlobal("business") ?? ''); ?></h1>
                </div>
                <!-- End Col -->

                <div class="col-sm-auto order-1 order-sm-2 text-sm-end mb-3">
                  <div class="mb-3">
                    <h2>Sale #</h2>
                    <span class="d-block"><?php echo e($order->order_id); ?></span>
                  </div>

                  <address class="text-dark">
                    <?php echo e($order->createdBy->name ?? ''); ?><br>
                    <?php echo e($order->createdBy->email ?? ''); ?><br>
                    <?php echo e($order->createdBy->address ?? ''); ?><br>
                  </address>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Row -->

              <div class="row justify-content-md-between mb-3">
                <div class="col-md">
                  <h4>Supplier:</h4>
                  <h4><?php echo e($order->supplier_name ?? ''); ?></h4>

                  <address>
                    <?php echo e($order->customer->phone ?? ''); ?><br>
                    <?php echo e($order->customer->email ?? ''); ?><br>
                    <?php echo e($order->customer->address ?? ''); ?>

                  </address>
                </div>
                <!-- End Col -->

                <div class="col-md text-md-end">
                  <dl class="row">
                    <dt class="col-sm-8">Order date:</dt>
                    <dd class="col-sm-4"><?php echo e(optional($order->created_at)->format('Y-m-d') ?? ''); ?></dd>
                  </dl>
                  <dl class="row">
                    <dt class="col-sm-8">Due date:</dt>
                    <dd class="col-sm-4"><?php echo e(optional($order->date_to)->format('Y-m-d') ?? ''); ?></dd>
                  </dl>
                </div>
                <!-- End Col -->
              </div>
              <!-- End Row -->

              <!-- Table -->
              <div class="table-responsive">
                <table class="table table-borderless table-nowrap table-align-middle">
                  <thead class="thead-light">
                    <tr>
                      <th>Item</th>
                      <th>Quantity</th>
                      <th>Price</th>
                      <th class="table-text-end">Amount</th>
                    </tr>
                  </thead>

                  <tbody>
                    <?php $__currentLoopData = $order->sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                      <th>
                        <span class="d-block h5 mb-0"> 
                          <?php echo e($sale->item->name ?? '-'); ?> <br>
                          <small> <?php echo e($sale->item->reference_number ?? '-'); ?> </small>
                        </span>
                        <span class="d-block fs-5"> <?php echo e($sale->item->description ?? ''); ?></span>
                      </th>
                      <td> <?php echo e($sale->quantity ?? ''); ?></td>
                      <td> <?php echo e(_number( $sale->selling_price ) ?? ''); ?> </td>
                      <td class="table-text-end">
                        <?php echo e(_number( $sale->quantity * $sale->selling_price )); ?> 
                      </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </tbody>
                </table>
              </div>
              <!-- End Table -->

              <hr class="my-5">

              <div class="row justify-content-md-end mb-3">
                <div class="col-md-8 col-lg-7">
                  <dl class="row text-sm-end">
                    <dt class="col-sm-6">Sub-Total:</dt>
                    <dd class="col-sm-6"> <?php echo e(_money( $order->sub_total ) ?? ''); ?> </dd>                    

                    <dt class="col-sm-6">Discount:</dt>
                    <dd class="col-sm-6"> <?php echo e(_money( $order->discount ) ?? ''); ?> </dd>                 

                    <dt class="col-sm-6">Total Paid:</dt>
                    <dd class="col-sm-6"> <?php echo e(_money( $order->amount_total) ?? ''); ?> </dd>

                    <dt class="col-sm-6">Cash Paid:</dt>
                    <dd class="col-sm-6"> <?php echo e(_money( $order->amount_paid) ?? ''); ?> </dd>
                  </dl>
                  <!-- End Row -->
                </div>
              </div>
              <!-- End Row -->

              <div class="mb-3">
                <h3>Thank you!</h3>
                <p>If you have any questions concerning this order, use the following contact information:</p>
              </div>

              <p class="small mb-0">&copy; <?php echo e(date('Y')); ?></p>
            </div>
          </div>
          <!-- End Card -->

          <!-- Footer -->
<!--           <div class="d-flex justify-content-end d-print-none gap-3">
            <a class="btn btn-white" href="#">
              <i class="bi-file-earmark-arrow-down me-1"></i> PDF
            </a>

            <a class="btn btn-primary" href="javascript:;" onclick="window.print(); return false;">
              <i class="bi-printer me-1"></i> Print details
            </a>
          </div> -->
          <!-- End Footer -->
        </div>


        <div class="col-lg-4">
          <div id="stickyBlockStartPoint">
            <div class="js-sticky-block" data-hs-sticky-block-options='{
                   "parentSelector": "#stickyBlockStartPoint",
                   "breakpoint": "lg",
                   "startPoint": "#stickyBlockStartPoint",
                   "endPoint": "#stickyBlockEndPoint",
                   "stickyOffsetTop": 20
                 }'>
              <div class="d-grid gap-2 gap-sm-3 mb-2 mb-sm-3">
                <button class="btn btn-soft-primary preview-order-btn" data-bs-toggle="modal" class="preview-order-btn" data-id="<?php echo e($order->id); ?>" data-bs-target=".preview-order-modal">
                  <i class="bi-download me-1"></i> PRINT
                </button>                     
                        
<!--                 <?php if($order->approved_by): ?>
                  Approved By: <?php echo e(optional($order->approvedBy)->name ?? ''); ?>

                <?php else: ?>
                    <?php if( auth()->user()->can('approve order') ): ?>
                    <button type="button" class="btn btn-soft-info m-1 pending-order-btn" data-bs-toggle="modal" data-id="<?php echo e($order->id); ?>" data-bs-target=".pending-order-modal">
                      <i class="me-1"></i> Approve Now
                    </button>  
                    <?php else: ?>
                    <a href="#" class="btn btn-soft-danger"> Waiting Approval </a>
                    <?php endif; ?>
                <?php endif; ?> -->
              </div>

              <div>
                <table class="table">
                  <thead  class="thead-light">
                    <tr>
                      <th>#</th>
                      <th>Paid Amount</th>
                      <th>Balance</th>
                      <th>Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php $__currentLoopData = $order->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr> 
                      <th scope="row"> <?php echo e($key + 1); ?> </th> 
                      <td> <?php echo e(_money(  $payment->amount )); ?> </td>
                      <td> <?php echo e(_money(  $payment->balance )); ?> </td>
                      <td> <?php echo e($payment->created_at); ?> </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </tbody>

                </table>
              </div>

            </div>
          </div>
        </div>

 
      </div>
    </div>
    <!-- End Content -->

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/orders/show.blade.php ENDPATH**/ ?>