/**
 * Utility functions for exporting data to various formats
 */

/**
 * Convert array of objects to CSV format
 */
export function arrayToCSV(data: any[], filename: string = 'export.csv'): void {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  // Get headers from first object
  const headers = Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    // Header row
    headers.join(','),
    // Data rows
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Handle values that might contain commas or quotes
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value || '';
      }).join(',')
    )
  ].join('\n');

  downloadFile(csvContent, filename, 'text/csv');
}

/**
 * Convert array of objects to JSON format
 */
export function arrayToJSON(data: any[], filename: string = 'export.json'): void {
  if (!data || data.length === 0) {
    alert('No data to export');
    return;
  }

  const jsonContent = JSON.stringify(data, null, 2);
  downloadFile(jsonContent, filename, 'application/json');
}

/**
 * Download file with given content
 */
function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
}

/**
 * Format data for export by cleaning up complex objects
 */
export function formatDataForExport(data: any[]): any[] {
  return data.map(item => {
    const formatted: any = {};
    
    for (const [key, value] of Object.entries(item)) {
      // Skip complex objects and functions
      if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
        continue;
      }
      
      // Format dates
      if (value instanceof Date) {
        formatted[key] = value.toISOString().split('T')[0];
      }
      // Format currency values
      else if (typeof value === 'number' && (key.includes('amount') || key.includes('price') || key.includes('total'))) {
        formatted[key] = value.toFixed(2);
      }
      // Keep other values as is
      else {
        formatted[key] = value;
      }
    }
    
    return formatted;
  });
}

/**
 * Export winners report data
 */
export function exportWinnersReport(data: any[], format: 'csv' | 'json' = 'csv'): void {
  const formattedData = formatDataForExport(data);
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `winners-report-${timestamp}.${format}`;
  
  if (format === 'csv') {
    arrayToCSV(formattedData, filename);
  } else {
    arrayToJSON(formattedData, filename);
  }
}

/**
 * Export sales report data
 */
export function exportSalesReport(data: any[], format: 'csv' | 'json' = 'csv'): void {
  const formattedData = formatDataForExport(data);
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `sales-report-${timestamp}.${format}`;
  
  if (format === 'csv') {
    arrayToCSV(formattedData, filename);
  } else {
    arrayToJSON(formattedData, filename);
  }
}

/**
 * Export inventory report data
 */
export function exportInventoryReport(data: any[], format: 'csv' | 'json' = 'csv'): void {
  const formattedData = formatDataForExport(data);
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `inventory-report-${timestamp}.${format}`;
  
  if (format === 'csv') {
    arrayToCSV(formattedData, filename);
  } else {
    arrayToJSON(formattedData, filename);
  }
}

/**
 * Export refunds report data
 */
export function exportRefundsReport(data: any[], format: 'csv' | 'json' = 'csv'): void {
  const formattedData = formatDataForExport(data);
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `refunds-report-${timestamp}.${format}`;
  
  if (format === 'csv') {
    arrayToCSV(formattedData, filename);
  } else {
    arrayToJSON(formattedData, filename);
  }
}

/**
 * Export deposits report data
 */
export function exportDepositsReport(data: any[], format: 'csv' | 'json' = 'csv'): void {
  const formattedData = formatDataForExport(data);
  const timestamp = new Date().toISOString().split('T')[0];
  const filename = `deposits-report-${timestamp}.${format}`;
  
  if (format === 'csv') {
    arrayToCSV(formattedData, filename);
  } else {
    arrayToJSON(formattedData, filename);
  }
}
