

<?php $__env->startSection('content'); ?>
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Inventory Report</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end">
                <a href="<?php echo e(route('items.index')); ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Items
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="card-title mb-0">Inventory Items</h5>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                        <i class="bi bi-funnel me-1"></i> Filters
                    </button>
                </div>
            </div>
        </div>

        <div class="collapse" id="filterCollapse">
            <div class="card-body border-bottom">
                <form method="GET" class="mb-0">
                    <input type="hidden" name="from" value="<?php echo e(request()->from); ?>">
                    <input type="hidden" name="to" value="<?php echo e(request()->to); ?>">

                    <div class="row g-3">
                        <div class="col-md-9">
                            <div class="d-flex align-items-center h-100">
                                <div class="flex-shrink-0">
                                    <label class="form-label mb-0 me-2">Auction Type:</label>
                                </div>
                                <div class="flex-grow-1">
                                    <select class="form-select form-select-sm" name="auction_type_id" autocomplete="off">
                                        <option value="0">All Types</option>
                                        <?php $__currentLoopData = App\Models\AuctionType::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $auction_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option <?php if(request()->auction_type_id == $auction_type->id): ?> selected <?php endif; ?> value="<?php echo e($auction_type->id); ?>">
                                            <?php echo e($auction_type->name ?? ''); ?>

                                        </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                <i class="bi bi-search me-1"></i> Apply
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th>#</th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.items.inputs.name'); ?>
                            </th>
                            <th class="text-right">
                                Target Price
                            </th>
                            <th class="text-center">
                                Created Date
                            </th>
                            <th class="text-left">
                                <?php echo app('translator')->get('crud.items.inputs.description'); ?>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($key + 1); ?></td>
                            <td class="table-column-ps-0">
                                <a class="d-flex align-items-center" href="/items/<?php echo e($item->id ?? '-'); ?>">
                                    <div class="flex-shrink-0">
                                        <img class="avatar avatar-lg" src="<?php echo e($item->image ?? '-'); ?>" alt="Image Description">
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h5 class="text-inherit mb-0">
                                            <?php echo e($item->name ?? '-'); ?>

                                        </h5>
                                    </div>
                                </a>
                            </td>
                            <td class="text-right">
                                <?php echo e(_money($item->target_amount)); ?>

                            </td>
                            <td class="text-center">
                                <?php echo e($item->created_at ?? '-'); ?>

                            </td>
                            <td><?php echo e($item->description ?? '-'); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">No inventory items found</td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr class="bg-light">
                            <th></th>
                            <th></th>
                            <th class="text-right">
                                <span class="h4 text-info">
                                    <?php echo e(_money($items->sum('target_amount'))); ?>

                                </span>
                            </th>
                            <th></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
    dataTableBtn(".js-datatable", null, [0, 'asc'])
  });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/app/reports/inventory-report.blade.php ENDPATH**/ ?>